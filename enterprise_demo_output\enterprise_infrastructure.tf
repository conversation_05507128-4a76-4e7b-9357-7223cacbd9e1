# Enterprise Infrastructure as Code
# Multi-cloud, highly available, auto-scaling infrastructure

terraform {
  required_version = ">= 1.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
  }
}

# Multi-AZ VPC for high availability
resource "aws_vpc" "enterprise_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "enterprise-data-pipeline-vpc"
    Environment = "production"
    Project     = "enterprise-pipeline"
  }
}

# EKS Cluster with enterprise features
resource "aws_eks_cluster" "enterprise_cluster" {
  name     = "enterprise-data-pipeline"
  role_arn = aws_iam_role.cluster_role.arn
  version  = "1.28"

  vpc_config {
    subnet_ids              = aws_subnet.private[*].id
    endpoint_private_access = true
    endpoint_public_access  = true
    public_access_cidrs     = ["0.0.0.0/0"]
  }

  encryption_config {
    provider {
      key_arn = aws_kms_key.cluster_key.arn
    }
    resources = ["secrets"]
  }

  enabled_cluster_log_types = ["api", "audit", "authenticator", "controllerManager", "scheduler"]

  depends_on = [
    aws_iam_role_policy_attachment.cluster_policy,
    aws_iam_role_policy_attachment.service_policy,
  ]
}

# Auto-scaling node groups
resource "aws_eks_node_group" "enterprise_nodes" {
  cluster_name    = aws_eks_cluster.enterprise_cluster.name
  node_group_name = "enterprise-nodes"
  node_role_arn   = aws_iam_role.node_role.arn
  subnet_ids      = aws_subnet.private[*].id

  instance_types = ["m5.2xlarge", "m5.4xlarge"]

  scaling_config {
    desired_size = 10
    max_size     = 100
    min_size     = 5
  }

  update_config {
    max_unavailable = 2
  }

  depends_on = [
    aws_iam_role_policy_attachment.node_policy,
    aws_iam_role_policy_attachment.cni_policy,
    aws_iam_role_policy_attachment.registry_policy,
  ]
}

# RDS Aurora for enterprise metadata
resource "aws_rds_cluster" "enterprise_metadata" {
  cluster_identifier      = "enterprise-metadata"
  engine                 = "aurora-postgresql"
  engine_version         = "14.9"
  database_name          = "enterprise_metadata"
  master_username        = "enterprise_admin"
  manage_master_user_password = true

  backup_retention_period = 35
  preferred_backup_window = "07:00-09:00"

  storage_encrypted = true
  kms_key_id       = aws_kms_key.rds_key.arn

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.enterprise.name

  enabled_cloudwatch_logs_exports = ["postgresql"]

  tags = {
    Name        = "enterprise-metadata-cluster"
    Environment = "production"
  }
}

# ElastiCache Redis for enterprise caching
resource "aws_elasticache_replication_group" "enterprise_cache" {
  replication_group_id       = "enterprise-cache"
  description                = "Enterprise pipeline cache cluster"

  node_type                  = "cache.r6g.2xlarge"
  port                       = 6379
  parameter_group_name       = "default.redis7"

  num_cache_clusters         = 6
  automatic_failover_enabled = true
  multi_az_enabled          = true

  subnet_group_name = aws_elasticache_subnet_group.enterprise.name
  security_group_ids = [aws_security_group.cache.id]

  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                 = random_password.cache_auth.result

  log_delivery_configuration {
    destination      = aws_cloudwatch_log_group.cache_slow.name
    destination_type = "cloudwatch-logs"
    log_format       = "text"
    log_type         = "slow-log"
  }
}

# S3 buckets for enterprise data storage
resource "aws_s3_bucket" "enterprise_data" {
  bucket = "enterprise-data-pipeline-${random_id.bucket_suffix.hex}"
}

resource "aws_s3_bucket_versioning" "enterprise_data" {
  bucket = aws_s3_bucket.enterprise_data.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_encryption" "enterprise_data" {
  bucket = aws_s3_bucket.enterprise_data.id

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = aws_kms_key.s3_key.arn
        sse_algorithm     = "aws:kms"
      }
    }
  }
}

# CloudWatch for enterprise monitoring
resource "aws_cloudwatch_log_group" "enterprise_logs" {
  name              = "/aws/enterprise-pipeline"
  retention_in_days = 90
  kms_key_id        = aws_kms_key.logs_key.arn
}

# Application Load Balancer
resource "aws_lb" "enterprise_alb" {
  name               = "enterprise-pipeline-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection = true

  access_logs {
    bucket  = aws_s3_bucket.alb_logs.bucket
    prefix  = "enterprise-alb"
    enabled = true
  }
}

# Random resources for unique naming
resource "random_id" "bucket_suffix" {
  byte_length = 8
}

resource "random_password" "cache_auth" {
  length  = 32
  special = true
}
