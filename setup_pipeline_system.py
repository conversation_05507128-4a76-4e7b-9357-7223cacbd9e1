"""
Setup script for the Multi-Agent Data Pipeline System
This script helps users set up the system and verify dependencies.
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path
from typing import List, Dict, Any

class PipelineSystemSetup:
    """Setup and verification for the pipeline system."""
    
    def __init__(self):
        self.required_packages = [
            "openai",
            "langchain",
            "langchain-community", 
            "prefect",
            "requests",
            "pathlib",
            "json",
            "typing"
        ]
        
        self.optional_packages = [
            "pyautogen",
            "streamlit",
            "unstructured",
            "pinecone-client",
            "cohere",
            "tavily-python"
        ]
        
        self.directories_to_create = [
            "generated_pipelines",
            "agents",
            "pipeline_templates",
            "config"
        ]
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8 or higher is required")
            print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
            return False
        
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def check_package(self, package_name: str) -> bool:
        """Check if a package is installed."""
        try:
            importlib.import_module(package_name.replace("-", "_"))
            return True
        except ImportError:
            return False
    
    def install_package(self, package_name: str) -> bool:
        """Install a package using pip."""
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            return True
        except subprocess.CalledProcessError:
            return False
    
    def check_dependencies(self) -> Dict[str, bool]:
        """Check all dependencies."""
        print("\n📦 Checking dependencies...")
        
        results = {}
        
        # Check required packages
        print("\nRequired packages:")
        for package in self.required_packages:
            installed = self.check_package(package)
            status = "✅" if installed else "❌"
            print(f"   {status} {package}")
            results[package] = installed
        
        # Check optional packages
        print("\nOptional packages:")
        for package in self.optional_packages:
            installed = self.check_package(package)
            status = "✅" if installed else "⚠️"
            print(f"   {status} {package}")
            results[package] = installed
        
        return results
    
    def install_missing_packages(self, results: Dict[str, bool]) -> None:
        """Install missing required packages."""
        missing_required = [
            pkg for pkg in self.required_packages 
            if not results.get(pkg, False)
        ]
        
        if missing_required:
            print(f"\n🔧 Installing missing required packages...")
            for package in missing_required:
                print(f"   Installing {package}...")
                if self.install_package(package):
                    print(f"   ✅ {package} installed successfully")
                else:
                    print(f"   ❌ Failed to install {package}")
        else:
            print("\n✅ All required packages are installed")
    
    def create_directories(self) -> None:
        """Create necessary directories."""
        print("\n📁 Creating directories...")
        
        for directory in self.directories_to_create:
            path = Path(directory)
            if not path.exists():
                path.mkdir(parents=True, exist_ok=True)
                print(f"   ✅ Created {directory}/")
            else:
                print(f"   ✅ {directory}/ already exists")
    
    def create_config_template(self) -> None:
        """Create a basic configuration template."""
        config_path = Path("config/azure_config.py")
        
        if not config_path.exists():
            config_content = '''"""
Azure OpenAI Configuration Template
Update these values with your actual credentials.
"""

# Azure OpenAI Configuration
AZURE_CONFIG = {
    "model": "gpt-4o",
    "api_type": "azure",
    "base_url": "https://your-endpoint.openai.azure.com/",
    "api_key": "your-api-key-here",
    "api_version": "2023-05-15"
}

# Environment variables (recommended)
import os

def get_azure_config():
    """Get Azure config from environment variables or defaults."""
    return {
        "model": os.getenv("AZURE_MODEL", "gpt-4o"),
        "api_type": "azure",
        "base_url": os.getenv("AZURE_ENDPOINT", "https://your-endpoint.openai.azure.com/"),
        "api_key": os.getenv("AZURE_API_KEY", "your-api-key-here"),
        "api_version": os.getenv("AZURE_API_VERSION", "2023-05-15")
    }
'''
            
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"   ✅ Created {config_path}")
        else:
            print(f"   ✅ {config_path} already exists")
    
    def verify_data_eng_database(self) -> bool:
        """Verify that the Data_Eng_Database directory exists."""
        data_eng_path = Path("Data_Eng_Database")
        
        if not data_eng_path.exists():
            print("❌ Data_Eng_Database directory not found")
            print("   Please ensure you're running this from the correct directory")
            return False
        
        # Check for key subdirectories
        required_subdirs = [
            "data_loading",
            "chunking", 
            "embeddings",
            "vector_stores",
            "llms",
            "tools"
        ]
        
        missing_dirs = []
        for subdir in required_subdirs:
            if not (data_eng_path / subdir).exists():
                missing_dirs.append(subdir)
        
        if missing_dirs:
            print(f"❌ Missing Data_Eng_Database subdirectories: {missing_dirs}")
            return False
        
        print("✅ Data_Eng_Database structure verified")
        return True
    
    def run_demo_test(self) -> bool:
        """Run a quick demo test."""
        print("\n🧪 Running demo test...")
        
        try:
            # Import and run demo
            from demo_pipeline_system import DemoDataPipelineSystem
            
            demo_system = DemoDataPipelineSystem()
            result = demo_system.create_demo_pipeline("Test pipeline for setup verification")
            
            if result and "pipeline_code" in result:
                print("✅ Demo test passed")
                return True
            else:
                print("❌ Demo test failed")
                return False
                
        except Exception as e:
            print(f"❌ Demo test failed: {str(e)}")
            return False
    
    def setup(self) -> bool:
        """Run the complete setup process."""
        print("🚀 Multi-Agent Data Pipeline System Setup")
        print("=" * 50)
        
        # Check Python version
        if not self.check_python_version():
            return False
        
        # Verify Data_Eng_Database
        if not self.verify_data_eng_database():
            return False
        
        # Check dependencies
        results = self.check_dependencies()
        
        # Install missing packages
        self.install_missing_packages(results)
        
        # Create directories
        self.create_directories()
        
        # Create config template
        self.create_config_template()
        
        # Run demo test
        demo_success = self.run_demo_test()
        
        print("\n" + "=" * 50)
        
        if demo_success:
            print("🎉 Setup completed successfully!")
            print("\nNext steps:")
            print("1. Update config/azure_config.py with your Azure OpenAI credentials")
            print("2. Run: python demo_pipeline_system.py")
            print("3. Or run: python multi_agent_pipeline_system.py")
            print("4. For web interface: python multi_agent_pipeline_system.py web")
            return True
        else:
            print("⚠️ Setup completed with warnings")
            print("Please check the error messages above and resolve any issues")
            return False

def main():
    """Main setup function."""
    setup = PipelineSystemSetup()
    success = setup.setup()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
