"""
Demo script for the Multi-Agent Data Pipeline System
This script demonstrates how to use the system without requiring all dependencies.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

# Mock AutoGen classes for demo purposes
class MockAssistantAgent:
    def __init__(self, name: str, system_message: str, llm_config: Dict):
        self.name = name
        self.system_message = system_message
        self.llm_config = llm_config
    
    def initiate_chat(self, other_agent, message: str, max_turns: int = 1):
        # Mock response for demo
        return MockChatResult()

class MockUserProxyAgent:
    def __init__(self, name: str, human_input_mode: str, max_consecutive_auto_reply: int, code_execution_config: bool):
        self.name = name
    
    def initiate_chat(self, other_agent, message: str):
        return MockChatResult()

class MockChatResult:
    def __init__(self):
        self.chat_history = [
            {
                "content": """{
                    "data_sources": ["pdf"],
                    "pdf_parsing_strategy": "unstructured",
                    "chunking_strategy": "recursive",
                    "embedding_provider": "openai",
                    "vector_store": "pinecone",
                    "llm_provider": "azure_openai",
                    "external_tools": [],
                    "use_case": "rag",
                    "requirements": {
                        "chunk_size": 1000,
                        "chunk_overlap": 200,
                        "max_results": 10,
                        "temperature": 0.7
                    }
                }"""
            }
        ]

# Mock the imports for demo
import sys
from unittest.mock import MagicMock

# Create mock modules
mock_autogen = MagicMock()
mock_autogen.AssistantAgent = MockAssistantAgent
mock_autogen.UserProxyAgent = MockUserProxyAgent

sys.modules['autogen'] = mock_autogen

# Now import our system
from multi_agent_pipeline_system import DataPipelineAgentSystem

class DemoDataPipelineSystem:
    """Demo version of the pipeline system that works without all dependencies."""
    
    def __init__(self):
        self.output_path = Path("generated_pipelines")
        self.output_path.mkdir(exist_ok=True)
        
    def create_demo_pipeline(self, user_requirements: str) -> Dict[str, Any]:
        """Create a demo pipeline to show the system capabilities."""
        
        print(f"🤖 Analyzing requirements: {user_requirements}")
        print("📋 Creating pipeline plan...")
        
        # Demo plan based on the user requirements
        plan = {
            "data_sources": ["pdf"],
            "pdf_parsing_strategy": "unstructured",
            "chunking_strategy": "recursive", 
            "embedding_provider": "openai",
            "vector_store": "pinecone",
            "llm_provider": "azure_openai",
            "external_tools": [],
            "use_case": "rag",
            "requirements": {
                "chunk_size": 1000,
                "chunk_overlap": 200,
                "max_results": 10,
                "temperature": 0.7
            }
        }
        
        print("✅ Plan created:")
        for key, value in plan.items():
            print(f"   {key}: {value}")
        
        print("\n🔧 Selecting code components...")
        
        # Demo components
        components = {
            "data_loading": "# PDF parsing with Unstructured library",
            "chunking": "# Recursive chunking strategy", 
            "embedding": "# OpenAI embeddings",
            "vector_store": "# Pinecone vector database",
            "llm": "# Azure OpenAI for RAG"
        }
        
        print("✅ Components selected:")
        for component in components.keys():
            print(f"   ✓ {component}")
        
        print("\n🔑 Generating configuration template...")
        
        # Demo configuration
        config_template = """# Configuration Template
# Fill in your API keys and configuration values

# OpenAI API key for embeddings
OPENAI_API_KEY=your_openai_api_key_here

# Azure OpenAI configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here

# Pinecone configuration
PINECONE_API_KEY=your_pinecone_api_key_here

# Additional Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RESULTS=10
TEMPERATURE=0.7
"""
        
        print("\n🏗️ Building final pipeline...")
        
        # Demo pipeline code
        pipeline_code = '''"""
Generated Data Pipeline
Created by Multi-Agent Pipeline System
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from prefect import task, flow

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
CONFIG = {
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "max_results": 10,
    "temperature": 0.7
}

def load_config():
    """Load configuration from environment variables."""
    config = CONFIG.copy()
    
    # Override with environment variables if available
    for key in config:
        env_key = key.upper()
        if env_key in os.environ:
            config[key] = os.environ[env_key]
    
    return config

@task(name="pdf_parsing_task")
def pdf_parsing_task(pdf_path: str, config: Dict[str, Any]) -> List[Dict]:
    """Parse PDF documents using Unstructured library."""
    logger.info(f"Parsing PDF: {pdf_path}")
    # Implementation would go here
    return [{"content": "Sample document content", "metadata": {"source": pdf_path}}]

@task(name="chunking_task") 
def chunking_task(documents: List[Dict], config: Dict[str, Any]) -> List[Dict]:
    """Chunk documents using recursive strategy."""
    logger.info(f"Chunking {len(documents)} documents")
    # Implementation would go here
    return [{"text": "Sample chunk", "metadata": {"chunk_id": 1}}]

@task(name="embedding_task")
def embedding_task(chunks: List[Dict], config: Dict[str, Any]) -> List[Dict]:
    """Generate embeddings using OpenAI."""
    logger.info(f"Generating embeddings for {len(chunks)} chunks")
    # Implementation would go here
    return [{"embedding": [0.1, 0.2, 0.3], "text": "Sample chunk"}]

@task(name="vector_store_task")
def vector_store_task(chunks: List[Dict], embeddings: List[Dict], config: Dict[str, Any]) -> Dict:
    """Store embeddings in Pinecone."""
    logger.info("Storing embeddings in Pinecone")
    # Implementation would go here
    return {"status": "success", "count": len(embeddings)}

@flow(name="custom_data_pipeline")
def custom_data_pipeline_flow(pdf_path: str = None, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Main data pipeline flow."""
    config = load_config()
    if config_override:
        config.update(config_override)
    
    logger.info("Starting custom data pipeline")
    results = {}
    
    try:
        # Data Loading Phase
        documents = pdf_parsing_task(pdf_path or "sample.pdf", config)
        results["documents"] = documents
        
        # Chunking Phase
        chunks = chunking_task(documents, config)
        results["chunks"] = chunks
        
        # Embedding Phase
        embeddings = embedding_task(chunks, config)
        results["embeddings"] = embeddings
        
        # Vector Store Phase
        vector_result = vector_store_task(chunks, embeddings, config)
        results["vector_store"] = vector_result
        
        logger.info("Pipeline completed successfully")
        return results
        
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        raise

if __name__ == "__main__":
    print("Running custom data pipeline...")
    result = custom_data_pipeline_flow()
    print("Pipeline completed successfully!")
    print(f"Results: {result}")
'''
        
        # Save demo files
        pipeline_file = self.output_path / "demo_pipeline.py"
        config_file = self.output_path / "demo_config.env"
        
        with open(pipeline_file, 'w', encoding='utf-8') as f:
            f.write(pipeline_code)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_template)
        
        print(f"✅ Pipeline generated successfully!")
        print(f"📁 Files saved:")
        print(f"   Pipeline: {pipeline_file}")
        print(f"   Config: {config_file}")
        
        return {
            "pipeline_code": pipeline_code,
            "config_template": config_template,
            "filename": str(pipeline_file),
            "components_used": list(components.keys()),
            "plan": plan
        }

def main():
    """Main demo function."""
    print("🚀 Multi-Agent Data Pipeline System Demo")
    print("=" * 50)
    
    # Create demo system
    demo_system = DemoDataPipelineSystem()
    
    # Example user requirement
    user_input = """I want you to build a pipeline which intakes a set of documents and treats that as a historical reference material and uses it to answer questions from another questionnaire document"""
    
    print(f"\n📝 User Request:")
    print(f"   {user_input}")
    print("\n" + "=" * 50)
    
    # Generate pipeline
    result = demo_system.create_demo_pipeline(user_input)
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\nNext steps:")
    print("1. Install required dependencies: pip install -r requirements.txt")
    print("2. Fill in your API keys in the config file")
    print("3. Run the generated pipeline")
    print("4. Customize as needed for your specific use case")

if __name__ == "__main__":
    main()
